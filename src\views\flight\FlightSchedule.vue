<template>
  <div class="p-4">
    <!-- 顶部工具栏 -->
    <div class="schedule-header">
      <!-- 左侧筛选器 -->
      <div class="filter-section">
        <a-select
          v-model:value="selectedAirport"
          class="custom-select"
          placeholder="请选择机场进行搜索"
          @change="handleAirportChange"
        >
          <a-select-option v-for="airport in airports" :key="airport" :value="airport">
            {{ airport }}
          </a-select-option>
        </a-select>
      </div>
      
      <!-- 中间日期显示和选择 -->
      <div class="date-section">
        <div class="current-date">
          {{ viewMode === 'day' ? dayjs(selectedDate).format('YYYY/MM/DD') + (dayjs(selectedDate).format('YYYY-MM-DD') === dayjs().format('YYYY-MM-DD') ? ' 今天' : '') : dayjs(selectedDate).format('YYYY年M月') }}
          <div class="task-count">共{{ todayTaskCount }}架次</div>
        </div>
        <a-dropdown>
          <a-button type="link">
            <DownOutlined />
          </a-button>
          <template #overlay>
            <a-menu @click="handleDateSelect">
              <a-menu-item v-for="date in dateOptions" :key="date.value">
                {{ date.label }}
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
      
      <!-- 右侧日/月切换 -->
      <div class="view-toggle" @click="toggleViewMode">
        <a-radio-group v-model:value="viewMode" button-style="solid">
          <a-radio-button value="day">日</a-radio-button>
          <a-radio-button value="month">月</a-radio-button>
        </a-radio-group>
      </div>
    </div>
    
    <!-- 时间段排期 -->
    <div v-if="viewMode === 'day'" class="schedule-timeline">
      <div v-for="timeSlot in timeSlots" :key="timeSlot.time" class="time-slot">
        <div class="time-label">{{ timeSlot.time }}</div>
        <div class="task-container-wrapper">
          <!-- 如果没有任务，显示添加按钮 -->
          <template v-if="timeSlot.tasks.length === 0">
            <div class="task-container">
              <div class="add-task-btn" @click="showAddTaskModal(timeSlot)">
                <PlusOutlined />
              </div>
            </div>
          </template>
          <!-- 如果有任务，显示所有任务 -->
          <template v-else>
            <div v-for="task in timeSlot.tasks" :key="task.id" class="task-container" :class="[`status-${task.status}`, { 'has-task': true }]">
              <div class="task-info">
                <div class="task-header">
                  <div class="task-time-status">
                    <div class="time">{{ task.flightTime }}</div>
                    <div class="status" :class="{ 'returning': task.isReturning }">{{ getFlightStatus(task) }}</div>
                  </div>
                  <div class="task-name">{{ task.name }}</div>
                </div>
                <div class="task-continuous">
                  <img :src="zhuyiIcon" alt="注意" class="zhuyi-icon" />
                  连续执行
                  <span v-if="task.isReturning" class="task-returning">（飞行任务手动返航！）</span>
                </div>
                <div class="task-actions">
                  <a-button type="primary" class="flight-btn" @click="handleFlight(task)">
                    <span>{{ getFlightButtonText(task) }}</span>
                    <img :src="qifeiIcon" alt="起飞" class="flight-icon" />
                  </a-button>
                  <a-dropdown>
                    <a-button type="link">
                      <SettingOutlined />
                    </a-button>
                    <template #overlay>
                      <a-menu>
                        <a-menu-item key="detail" @click="viewTaskDetail(task)">查看详情</a-menu-item>
                        <a-menu-item key="delete" @click="deleteTask(task)">删除</a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>

    <!-- 月视图日历 -->
    <div v-if="viewMode === 'month'" class="month-calendar" :style="monthCalendarStyle">
      <!-- 星期表头 -->
      <div class="calendar-header">
        <div v-for="day in weekDays" :key="day" class="week-day">{{ day }}</div>
      </div>
      
      <!-- 日历主体 -->
      <div class="calendar-body">
        <div v-for="(week, weekIndex) in calendarDays" :key="weekIndex" class="calendar-week">
          <!-- <div 
            v-for="day in week" 
            :key="day.date" 
            class="calendar-day" 
            :class="{ 
              'other-month': !day.isCurrentMonth,
              'today': dayjs(day.date).format('YYYY-MM-DD') === dayjs().format('YYYY-MM-DD')
            }"
            @click="handleDayClick(day)"
          > -->
          <div v-for="day in week" :key="day.date" class="calendar-day" :class="{ 'other-month': !day.isCurrentMonth }" @click="handleCalendarDayClick(day)">
            <div class="day-number">{{ day.dayNumber }}</div>
            <div class="task-counts">
              <div class="total-count">
                架次({{ getTaskCountsByDate(dayjs(day.date)).fullCoverage }})
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务类型图例 - 移到日历外部 -->
    <div v-if="viewMode === 'month'" class="task-types-legend">
      <div v-for="type in taskTypes" :key="type.value" class="legend-item">
        <span class="type-dot" :style="{ backgroundColor: type.color }"></span>
        <span class="type-name">{{ type.label }}</span>
      </div>
    </div>
    
    <!-- 添加任务弹窗 -->
    <a-modal
      v-model:open="addTaskModalVisible"
      title="添加任务"
      @ok="handleAddTaskConfirm"
      @cancel="handleAddTaskCancel"
    >
      <a-form :model="newTask" layout="vertical">
        <a-form-item label="机场">
          <a-select v-model:value="newTask.airport">
            <a-select-option v-for="airport in airports" :key="airport" :value="airport">
              {{ airport }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="起飞时间">
          <a-form-item-rest>
            <a-time-picker v-model:value="newTask.flightTime" format="HH:mm" />
          </a-form-item-rest>
        </a-form-item>
        <a-form-item label="日期">
          <a-form-item-rest>
            <a-date-picker v-model:value="newTask.date" />
          </a-form-item-rest>
        </a-form-item>
        <a-form-item label="任务名称">
          <a-input v-model:value="newTask.name" />
        </a-form-item>
        <a-form-item label="创建人">
          <a-input v-model:value="newTask.creator" />
        </a-form-item>
        <a-form-item label="状态">
          <a-select v-model:value="newTask.status">
            <a-select-option value="起飞">起飞</a-select-option>
            <a-select-option value="计划起飞">计划起飞</a-select-option>
            <a-select-option value="待飞行">待飞行</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 引用表格 -->
    <!-- <BasicTable @register="registerTable">
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable> -->
  </div>
</template>

<script lang="ts" name="basic-table-demo" setup>
import { ActionItem, BasicColumn, BasicTable, TableAction} from '/@/components/Table';
import { useListPage } from '/@/hooks/web/useListPage';
import { useGo } from '/@/hooks/web/usePage';
import { ref, onMounted, computed, createVNode } from 'vue';
import { message, Modal } from 'ant-design-vue';
import dayjs from 'dayjs';
import { defHttp } from '/@/utils/http/axios';
import type { BasicTableProps } from '/@/components/Table';
import qifeiIcon from '/@/assets/images/icon/qifei.png';
import zhuyiIcon from '/@/assets/images/icon/zhuyi.png';
import { DownOutlined, PlusOutlined, SettingOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue';

// 确保图标组件被使用
const iconComponents = {
  downIcon: DownOutlined,
  plusIcon: PlusOutlined,
  settingIcon: SettingOutlined
};

interface Task {
  id: string;
  name: string;
  date: string;
  airport: string;
  flightTime: string;
  creator: string;
  status: string;
  type?: string;
  isFlying: boolean;
  isReturning?: boolean;
  flightPath?: any;
  taskId?: string;
  sysOrgCode?: string;
  createTime?: string;
  updateTime?: string;
}

interface TimeSlot {
  time: string;
  tasks: Task[];
}

interface CalendarDay {
  date: string;
  dayNumber: number;
  isCurrentMonth: boolean;
  taskCounts: Record<string, number>;
}

interface ApiTaskData {
  id: string;
  taskId?: string;
  createBy?: string;
  createTime?: string;
  updateBy?: string;
  updateTime?: string;
  sysOrgCode?: string;
  status?: number;
  flightPath?: string;
  takeoffTime?: string; 
}

const go = useGo();
const apiData = ref<ApiTaskData[]>([]);
const scheduleList = ref<ApiTaskData[]>([]);


// 定义表格列
const columns: BasicColumn[] = [
  {
    title: '机场名称',
    dataIndex: 'airport',
    width: 200,
  },
  {
    title: '航班时间',
    dataIndex: 'flightTime',
    width: 150,
  },
  {
    title: '航班状态',
    dataIndex: 'status',
    width: 120,
  },
  {
    title: '创建人',
    dataIndex: 'creator',
    width: 120,
  },
];

// 获取表格操作
// const getTableAction = (record: ApiTaskData): ActionItem[] => {
//   return [
//     {
//       label: '查看详情',
//       onClick: () => {
//         // 先检查记录ID是否存在
//         if (!record || !record.id) {
//           message.error('任务ID不存在，无法查看详情');
//           return;
//         }
        
//         // 确保使用正确的参数名称和格式，添加source参数标识来源为飞行排期
//         go(`/flight/taskDetail?scheduleId=${record.id}&source=schedule`);
//       }
//     }
//   ];
// };

// 使用 useListPage 注册表格
const { register: registerTable } = useListPage({
  columns,
  api: getFlightScheduleList,
  rowKey: 'id',
  formConfig: {
    labelWidth: 100,
  },
  useSearchForm: true,
  showTableSetting: true,
  bordered: true,
  actionColumn: {
    width: 120,
    title: '操作',
    dataIndex: 'action',
    slots: { customRender: 'action' },
  },
} as BasicTableProps);

// 定义API接口
enum Api {
  list = '/flight/flightScheduleList/list',
}

// 获取飞行排期列表的函数
function getFlightScheduleList(params: any) {
  let startDate = params.date;
  let endDate = params.date;
  if(params.type === 'month'){
    startDate = params.startDate;
    endDate = params.endDate;
  }
  //添加筛选参数
  const superQuery = [
        {
          "field": "createTime",
          "rule": "ge",
          "val": startDate + " 00:00:00",
          "type": "datetime",
          "dbType": "string"
        },
        {
          "field": "createTime",
          "rule": "le",
          "val": endDate + " 23:59:59",
          "type": "datetime",
          "dbType": "string"
        },
      ]
  // 添加通用参数
  const queryParams = {
    //...params,
    pageNo: 1,
    pageSize: 500,
    superQueryMatchType: 'and',
    superQueryParams: JSON.stringify(superQuery)
     // 增大页面大小以获取更多数据
  };
  
  console.log('发送请求参数:', queryParams);
  
  return defHttp.get({
    url: Api.list,
    params: queryParams,
  });
}

// 初始化数据
onMounted(async () => {
  try {
    // 初始化获取当天数据

    const params = {
      date: dayjs().format('YYYY-MM-DD'),
      // 使用明确的日期范围而不是单个日期
      // createTime_begin: dayjs(selectedDate.value).format('YYYY-MM-DD 00:00:00'),
      // createTime_end: dayjs(selectedDate.value).format('YYYY-MM-DD 23:59:59')
    };
    console.log('请求参数:', params);
    
    const result = await getFlightScheduleList(params);
    console.log('API返回结果:', result);
    
    if (result?.records) {
      scheduleList.value = result.records;
      apiData.value = result.records;
      console.log('加载到航班数据数量:', result.records.length);
      
      // 使用API数据填充timeSlots
      updateTimeSlotsWithApiData();
      
      // 更新机场列表
      updateAirportsList();
    } else {
      console.log('API返回数据无records字段');
    }
  } catch (error) {
    console.error('获取排期数据失败:', error);
  }
});

// 使用API数据更新时间表
const updateTimeSlotsWithApiData = () => {
  // 先创建空的时间槽，从9点开始，不再显示0-8点
  const slots: TimeSlot[] = [];
  for (let hour = 9; hour <= 24; hour++) {
    slots.push({
      time: `${hour.toString().padStart(2, '0')}:00`,
      tasks: []
    });
  }
  
  console.log('创建空时间槽完成，共', slots.length, '个时间段');
  console.log('当前选中日期:', selectedDate.value);
  
  // 如果有API数据，填充到对应时间槽
  if (apiData.value && apiData.value.length > 0) {
    console.log('准备处理API数据，共', apiData.value.length, '条记录');
    
    let mappedCount = 0;
    let unmappedCount = 0;
    
    // 筛选当前选中日期的任务
    const filteredData = apiData.value.filter((item: ApiTaskData) => {
      if (!item.takeoffTime) return false;
      const taskDate = item.takeoffTime.split(' ')[0]; // 提取日期部分 YYYY-MM-DD
      return taskDate === selectedDate.value;
    });
    
    console.log('筛选后符合当前日期的任务数:', filteredData.length);
    
    filteredData.forEach((item: ApiTaskData, index) => {
      console.log(`处理第 ${index+1} 条数据:`, item);
      
      // 从任务起飞时间提取小时信息作为飞行时间
      const takeoffTime = item.takeoffTime || '';
      if (!takeoffTime) {
        console.log('  - 跳过: 无创建时间');
        unmappedCount++;
        return;
      }
      
      // 尝试提取时间部分
      const timePart = takeoffTime.split(' ')[1]; // 提取时间部分 HH:MM:SS
      if (!timePart) {
        console.log('  - 跳过: 时间格式错误');
        unmappedCount++;
        return;
      }
      
      const hourStr = timePart.split(':')[0];
      if (!hourStr) {
        console.log('  - 跳过: 无法提取小时');
        unmappedCount++;
        return;
      }
      
      const hour = parseInt(hourStr);
      console.log('  - 提取到小时:', hour);
      
      // 跳过0-8点的任务
      if (hour >= 0 && hour <= 8) {
        console.log('  - 跳过: 0-8点时间段不显示');
        unmappedCount++;
        return;
      }
      
      // 放宽时间段限制，使用更灵活的映射逻辑
      let targetSlot: TimeSlot | null = null;
      
      // 首先尝试精确匹配
      if (hour >= 9 && hour <= 24) {
        const index = hour - 9; // 调整索引，因为时间槽从9开始
        if (index < slots.length) {
          targetSlot = slots[index];
        }
      } 
      // 如果没有精确匹配（超过24点等异常情况）
      else {
        targetSlot = slots[slots.length - 1]; // 放在最后一个时间段
      }
      
              if (targetSlot) {
          // 允许在同一时间段添加多个任务，不再查找空闲时间段
          console.log(`  - 将任务添加到时间段: ${targetSlot.time}，当前已有${targetSlot.tasks.length}个任务`);
        
        // 处理飞行路径数据
        let flightPath: any = null;
        if (item.flightPath) {
          try {
            // 如果flightPath是字符串，尝试解析
            if (typeof item.flightPath === 'string') {
              flightPath = JSON.parse(item.flightPath);
            } else {
              flightPath = item.flightPath;
            }
          } catch (e) {
            console.log('  - 警告: 飞行路径数据解析失败', e);
            // 使用默认路径
            flightPath = {
              type: "LineString",
              coordinates: [
                [25.0566198429357, 102.89051413536073],
                [25.056590685505608, 102.89018154144289]
              ]
            };
          }
        } else {
          // 如果没有飞行路径，生成默认路径
          flightPath = {
            type: "LineString",
            coordinates: [
              [25.0566198429357, 102.89051413536073],
              [25.056590685505608, 102.89018154144289]
            ]
          };
        }
        
        // 使用数据库中的实际字段构建任务对象
        const task: Task = {
          id: item.id || '',
          name: `【飞行任务】${item.sysOrgCode || 'A01'}-${hourStr}时段`, // 构建名称
          date: item.createTime ? item.createTime.split(' ')[0] : dayjs().format('YYYY-MM-DD'),
          airport: item.sysOrgCode || 'A01', // 使用组织代码作为机场
          flightTime: timePart || '00:00:00',
          creator: item.createBy || '系统管理员',
          status: getStatusText(item.status), // 转换状态码为文本
          type: (item.status !== undefined ? item.status.toString() : '0'),
          isFlying: item.status === 2, // 状态为2表示已飞行
          flightPath: flightPath, // 添加飞行路径数据
          taskId: item.taskId || '', // 如果有关联的任务ID
          sysOrgCode: item.sysOrgCode || '',
          createTime: item.createTime || '',
          updateTime: item.updateTime || '',
          isReturning: false
        };
        
        console.log('  - 成功映射到时间段:', targetSlot.time);
        console.log('  - 飞行路径数据:', flightPath);
        targetSlot.tasks.push(task);
        mappedCount++;
      } else {
        console.log('  - 错误: 无法找到合适的时间段');
        unmappedCount++;
      }
    });
    
    console.log('数据映射完成:');
    console.log('- 成功映射:', mappedCount);
    console.log('- 未能映射:', unmappedCount);
  }
  
  console.log('最终时间槽状态:');
  slots.forEach((slot, index) => {
    console.log(`${slot.time}: ${slot.tasks.length} 个任务`);
  });
  
  // 无论是否有数据都使用API结果
  timeSlots.value = slots;
};

// 将状态码转换为文本
const getStatusText = (status) => {
  switch (status) {
    case 0: return '待飞行';
    case 1: return '计划起飞';
    case 2: return '起飞';
    default: return '待飞行';
  }
};

// 筛选器相关
const airports = ref(['A01']);
const selectedAirport = ref(undefined);

// 从API数据中获取机场列表
const updateAirportsList = () => {
  const airportSet = new Set<string>();
  
  // 从API数据中提取不重复的机场名称
  if (scheduleList.value && scheduleList.value.length > 0) {
    scheduleList.value.forEach(item => {
      if (item.sysOrgCode) {
        airportSet.add(item.sysOrgCode);
      }
    });
  }
  
  // 更新机场列表
  airports.value = Array.from(airportSet);
};

function handleAirportChange(value) {
  filterAirport(value);
}

function filterAirport(value) {
  if (value === 'A01' || !value) {
    apiData.value = scheduleList.value;
  } else {
    apiData.value = scheduleList.value.filter((item) => item.sysOrgCode === value);
  }
}

// 当前选中的日期
const selectedDate = ref(dayjs().format('YYYY-MM-DD'));

// 处理日期选择
const handleDateSelect = async ({ key }) => {
  if (viewMode.value === 'day') {
    selectedDate.value = key;
    
    // 切换日期时重新获取数据
    try {
      const params = {
        date: key
      };
      console.log('日视图', params);
      const result = await getFlightScheduleList(params);
      if (result?.records) {
        scheduleList.value = result.records;
        apiData.value = result.records;
        
        // 更新时间表
        updateTimeSlotsWithApiData();
        
        // 更新机场列表
        updateAirportsList();
      }
    } catch (error) {
      console.error('获取排期数据失败:', error);
    }
  } else {
    // 月视图下，更新当前年月
    const currentDate = dayjs();
    const newDate = currentDate.month(parseInt(key) - 1);
    selectedDate.value = newDate.format('YYYY-MM-DD');
    
    // 月视图加载对应月份的数据
    try {
      const monthStart = newDate.startOf('month').format('YYYY-MM-DD');
      const monthEnd = newDate.endOf('month').format('YYYY-MM-DD');
      
      const params = {
        startDate: monthStart,
        endDate: monthEnd,
        type: 'month'
      };
      
      const result = await getFlightScheduleList(params);
      if (result?.records) {
        scheduleList.value = result.records;
        apiData.value = result.records;
        
        // 更新机场列表
        updateAirportsList();
      }
    } catch (error) {
      console.error('获取月度排期数据失败:', error);
    }
  }
};

// 判断时间段是否应该显示
const shouldShowTimeSlot = (timeSlot: string) => {
  // 所有时间段都显示，每个小时默认显示一个框
  return true;
};

// 定义日期选项的类型
interface DateOption {
  label: string;
  value: string;
}

// 日期选项
const dateOptions = computed<DateOption[]>(() => {
  const options: DateOption[] = [];
  if (viewMode.value === 'day') {
    // 获取当前日期
    const today = dayjs();
    // 显示前三天、当天和后三天
    for (let i = -3; i <= 3; i++) {
      const date = today.add(i, 'day');
      const isToday = i === 0;
      options.push({
        label: date.format('YYYY/MM/DD') + (isToday ? ' 今天' : ''),
        value: date.format('YYYY-MM-DD')
      });
    }
  } else {
    // 月视图：显示1-12月
    for (let i = 1; i <= 12; i++) {
      options.push({
        label: `${i}月`,
        value: String(i).padStart(2, '0')
      });
    }
  }
  return options;
});

// 视图模式
const viewMode = ref('day');

// 时间段
const timeSlots = ref<TimeSlot[]>([]);

// 添加任务弹窗
const addTaskModalVisible = ref(false);
const newTask = ref({
  airport: '',
  flightTime: undefined as any,
  date: undefined as any,
  name: '',
  creator: '',
  status: '计划起飞'
});

// 显示添加任务弹窗
const showAddTaskModal = (timeSlot) => {
  // 跳转到新建任务页面，而不是显示弹窗
  go('/flight/addTask');
};

// 确认添加任务
const handleAddTaskConfirm = async () => {
  try {
    // 构建提交给后端的数据结构
    const flightScheduleData = {
      name: newTask.value.name,
      sysOrgCode: newTask.value.airport, // 机场代码作为组织代码
      createBy: newTask.value.creator,
      status: newTask.value.status === '起飞' ? 2 : (newTask.value.status === '计划起飞' ? 1 : 0), // 状态转换为数字
      createTime: dayjs(newTask.value.date).format('YYYY-MM-DD') + ' ' + dayjs(newTask.value.flightTime).format('HH:mm:ss')
    };
    
    // 调用后端API
    await defHttp.post({
      url: '/flight/flightScheduleList/add',
      data: flightScheduleData
    });
    
    // API调用成功后，更新本地视图
    const timeSlot = timeSlots.value.find(slot => slot.time === dayjs(newTask.value.flightTime).format('HH:mm'));
    if (timeSlot) {
      timeSlot.tasks.push({
        id: 'new_' + Date.now().toString(), // 临时ID，刷新后会被替换为真实ID
        name: newTask.value.name,
        airport: newTask.value.airport,
        flightTime: dayjs(newTask.value.flightTime).format('HH:mm'),
        date: dayjs(newTask.value.date).format('YYYY-MM-DD'),
        creator: newTask.value.creator,
        status: newTask.value.status,
        isFlying: newTask.value.status === '起飞',
        isReturning: false
      });
    }
    
    message.success('任务添加成功');
    
    // 重新获取数据刷新列表
    const params = {
      date: selectedDate.value
    };
    const result = await getFlightScheduleList(params);
    if (result?.records) {
      scheduleList.value = result.records;
      apiData.value = result.records;
      updateTimeSlotsWithApiData();
      updateAirportsList();
    }
    
    // 关闭对话框
    addTaskModalVisible.value = false;
  } catch (error) {
    console.error('添加任务失败：', error);
    message.error('添加任务失败，请稍后重试');
  }
};

// 取消添加任务
const handleAddTaskCancel = () => {
  addTaskModalVisible.value = false;
};

// 查看任务详情
const viewTaskDetail = (task) => {
  console.log('查看任务详情', task);
  // 先检查任务ID是否存在
  if (!task || !task.id) {
    message.error('任务ID不存在，无法查看详情');
    return;
  }
  
  // 确保使用正确的参数名称和格式，添加source参数标识来源为飞行排期
  go(`/flight/taskDetail?scheduleId=${task.id}&source=schedule`);
};

// 删除任务
const deleteTask = (task) => {
  Modal.confirm({
    title: '确认删除',
    icon: createVNode(ExclamationCircleOutlined),
    content: '是否确认删除该任务？',
    okText: '确认',
    cancelText: '取消',
    onOk: async () => {
      try {
        // 调用删除任务的API
        await defHttp.delete({
          url: '/flight/flightScheduleList/delete',
          params: { id: task.id }
        }, { joinParamsToUrl: true });
        
        // 前端更新UI
        const timeSlot = timeSlots.value.find(slot => slot.tasks.some(t => t.id === task.id));
        if (timeSlot) {
          timeSlot.tasks = timeSlot.tasks.filter(t => t.id !== task.id);
        }
        
        message.success('任务删除成功');
        
        // 刷新数据
        const params = {
          date: selectedDate.value
        };
        const result = await getFlightScheduleList(params);
        if (result?.records) {
          scheduleList.value = result.records;
          apiData.value = result.records;
          updateTimeSlotsWithApiData();
          updateAirportsList();
        }
      } catch (error) {
        console.error('删除任务失败：', error);
        message.error('删除任务失败，请稍后重试');
      }
    }
  });
};

// 在 script setup 部分添加计算属性
const todayTaskCount = computed(() => {
  // 使用当前选中的日期而不是今天的日期
  const currentSelectedDate = selectedDate.value;
  return timeSlots.value.reduce((count, slot) => count + slot.tasks.filter(task => dayjs(task.date).format('YYYY-MM-DD') === currentSelectedDate).length, 0);
});

// 在 script setup 部分添加
const weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];

const taskTypes = [
  { value: '1', label: '拍照', color: '#1890ff' },
  { value: '2', label: '直播', color: '#52c41a' },
  { value: '3', label: '全景', color: '#faad14' },
  { value: '4', label: '三维', color: '#f5222d' },
  { value: '5', label: '正射', color: '#722ed1' },
  { value: '6', label: '视频', color: '#13c2c2' }
];

// 生成日历数据
const calendarDays = computed<CalendarDay[][]>(() => {
  const currentDate = dayjs(selectedDate.value);
  const firstDayOfMonth = currentDate.startOf('month');
  const lastDayOfMonth = currentDate.endOf('month');
  
  // 获取当月第一天是星期几（0-6，0是周日）
  const firstDayWeek = firstDayOfMonth.day() || 7; // 转换为1-7，1是周一
  
  const days: CalendarDay[][] = [];
  let currentWeek: CalendarDay[] = [];
  
  // 添加上个月的日期
  for (let i = firstDayWeek - 1; i > 0; i--) {
    const date = firstDayOfMonth.subtract(i, 'day');
    currentWeek.push({
      date: date.format('YYYY-MM-DD'),
      dayNumber: date.date(),
      isCurrentMonth: false,
      taskCounts: getTaskCountsByDate(date)
    });
  }
  
  // 添加当月的日期
  for (let i = 1; i <= lastDayOfMonth.date(); i++) {
    if (currentWeek.length === 7) {
      days.push(currentWeek);
      currentWeek = [];
    }
    
    const date = firstDayOfMonth.date(i);
    currentWeek.push({
      date: date.format('YYYY-MM-DD'),
      dayNumber: i,
      isCurrentMonth: true,
      taskCounts: getTaskCountsByDate(date)
    });
  }
  
  // 添加下个月的日期
  while (currentWeek.length < 7) {
    const date = lastDayOfMonth.add(currentWeek.length - lastDayOfMonth.date() + 1, 'day');
    currentWeek.push({
      date: date.format('YYYY-MM-DD'),
      dayNumber: date.date(),
      isCurrentMonth: false,
      taskCounts: getTaskCountsByDate(date)
    });
  }
  
  days.push(currentWeek);
  return days;
});

// 获取指定日期的任务数量统计
const getTaskCountsByDate = (date: dayjs.Dayjs) => {
  const dateStr = date.format('YYYY-MM-DD');
 
  
  // 从API数据中筛选出指定日期的任务
  const tasks = apiData.value ? apiData.value.filter(item => {
    // 使用createTime作为日期判断依据
    if (!item.createTime) return false;
    const taskDate = dayjs(item.createTime.split(' ')[0]).format('YYYY-MM-DD');
    return taskDate === dateStr;
  }) : [];
  
  // 统计任务数量 - 假设所有任务都是全覆盖
  // 可以根据status或其他字段进行更细致的分类
  const fullCoverageCount = tasks.length;
  
  return {
    fullCoverage: fullCoverageCount
  };
};


// 修改月视图的样式
const monthCalendarStyle = computed(() => ({
  height: 'calc(100vh - 200px)',
  overflowY: 'auto' as const
}));

// 处理航班起飞
const handleFlight = (task: Task) => {
  // 修改为跳转到任务详情页面
  viewTaskDetail(task);
};

// 在 script setup 部分添加
const isTimePassed = (time: string) => {
  const now = dayjs();
  const today = now.format('YYYY-MM-DD');
  const taskTime = dayjs(`${today} ${time}`);
  return now.isAfter(taskTime);
};

// 修改原有的 isTaskFailed 函数
const isTaskFailed = (task: Task | null) => {
  if (!task) return false;
  const taskTime = dayjs(task.date + ' ' + task.flightTime);
  const now = dayjs();
  return now.isAfter(taskTime) && task.status !== '起飞';
};

// 修改 getFlightStatus 函数
const getFlightStatus = (task: Task) => {
  if (!task) return '';
  
  // 根据任务的isFlying状态或status字段判断
  if (task.isFlying || task.status === '起飞' || task.status === '飞行中') {
    return '已执行';
  } else {
    return '待飞行';
  }
}

// 获取飞行按钮显示文字
const getFlightButtonText = (task: Task) => {
  if (!task) return '';
  
  const status = getFlightStatus(task);
  if (status === '已执行') {
    return '已执行';
  } else {
    return '执行';
  }
}

// 新增日历点击事件
const handleCalendarDayClick = async (day: CalendarDay) => {
  if (viewMode.value === 'month') {
    // 如果是其他月份的日期，不处理点击
    if (!day.isCurrentMonth) {
      return;
    }
    
    // 检查该日期是否有飞行任务
    const taskCounts = getTaskCountsByDate(dayjs(day.date));
    if (taskCounts.fullCoverage === 0) {
      message.info('当天无飞行任务');
      return;
    }
    
    // 更新选中的日期
    selectedDate.value = day.date;
    
    // 切换到日视图
    viewMode.value = 'day';
    
    // 重新获取该日期的数据
    try {
      const params = {
        date: day.date
      };
      console.log('月视图点击日期，切换到日视图，加载日期:', day.date);
      
      const result = await getFlightScheduleList(params);
      if (result?.records) {
        scheduleList.value = result.records;
        apiData.value = result.records;
        
        // 更新时间表显示该日期的数据
        updateTimeSlotsWithApiData();
        
        // 更新机场列表
        updateAirportsList();
        
        console.log('成功加载', day.date, '的数据，共', result.records.length, '条记录');
      }
    } catch (error) {
      console.error('获取选中日期的排期数据失败:', error);
      message.error('获取该日期数据失败，请稍后重试');
    }
  }
};
const toggleViewMode = async () => {
  if (viewMode.value === 'day') {
    console.log('切换到月视图:',  calendarDays.value);
    //获取日历数据中的第一天和最后一天
    const firstDay = calendarDays.value[0][0].date;
    const lastDay = calendarDays.value[calendarDays.value.length - 1][6].date;
    console.log('firstDay:', firstDay, 'lastDay:', lastDay);

    // 加载当前日期的数据
    try {
      const params = {
        startDate: firstDay,
        endDate: lastDay,
        type: 'month'
      };
      const result =  await getFlightScheduleList(params);
      if(result?.records) {
        apiData.value = result.records;
        console.log('成功加载月视图数据，共', result.records.length, '条记录');
      }
    } catch (error) {
      console.error('获取选中日期的排期数据失败:', error);
    }
  }

}
</script>

<style lang="less" scoped>
.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  .filter-section {
    display: flex;
    align-items: center;
    gap: 12px;
    width: 200px;

    .filter-buttons {
      display: flex;
      gap: 6px;

      :deep(.ant-btn) {
        font-size: 12px;
        padding: 0 8px;
        height: 24px;
      }
    }

    .filter-options {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-left: auto;

      :deep(.ant-checkbox-wrapper) {
        font-size: 12px;
      }
    }
  }
  
  .date-section {
    display: flex;
    align-items: center;
    gap: 10px;
    flex: 1;
    justify-content: center;
    
    .current-date {
      font-size: 16px;
      font-weight: 500;
      display: flex;
      flex-direction: column;
      align-items: center;
      
      .task-count {
        font-size: 11px;
        color: rgba(100, 100, 100, 0.65);
        margin-top: 2px;
      }
    }
  }
  
  .view-toggle {
    width: 200px;
    display: flex;
    justify-content: flex-end;
  }
}

.schedule-timeline {
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
  padding-right: 0;
  padding-bottom: 40px;
  border-top: 1px solid rgba(192, 192, 192, 0.15);
  
  &::-webkit-scrollbar {
    display: none; // 隐藏滚动条
  }
  
  .time-slot {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    position: relative;
    padding: 10px 0;
    min-height: auto;
    flex-shrink: 0;
    border-bottom: 1px solid rgba(192, 192, 192, 0.15);
    
    .time-label {
      width: 30px;
      text-align: right;
      color: rgba(0, 0, 0, 0.45);
      padding-top: 15px;
      position: relative;
    }
    
    .task-container-wrapper {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 10px;
      min-height: 60px;
      
        .task-container {
          min-height: 30px;
          border: 1px dashed #d9d9d9;
          border-radius: 50px;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 10px;
          margin: 0;
          width: 100%;
        
        &.has-task {
          border-style: solid;
          min-height: 55px;
          height: 55px;
          
          &.status-起飞 {
            border-color: #0083fd;
            background-color: #0083fd;
            
            .ant-btn-primary {
              background: white;
              border-color: white;
              color: #096dd9;
              
              &:hover {
                background: #e6e6e6;
                border-color: #e6e6e6;
              }
            }
          }
          
          &.status-计划起飞 {
            border-color: #1066c1;
            background-color: #1066c1;
            
            .ant-btn-primary {
              background: white;
              border-color: white;
              color: #1066c1;
              
              &:hover {
                background: rgba(255, 255, 255, 0.9);
                border-color: rgba(255, 255, 255, 0.9);
              }
            }
          }
          
          &.status-待飞行 {
            border-color: #3878e6;
            background-color: #3878e6;
            
            .ant-btn-primary {
              background: #ffffff;
              border-color: #ffffff;
              color: #3878e6;
              
              &:hover {
                background: #40a9ff;
                border-color: #40a9ff;
              }
            }
          }
        }
        
        .task-info {
          width: 100%;
          color: white;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 20px;
          
          .task-header {
            flex: 1;
            display: flex;
            flex-direction: column;
            
            .task-time-status {
              display: flex;
              align-items: center;
              gap: 10px;
              margin-bottom: 0;
              
              .time {
                font-size: 14px;
                font-weight: 500;
              }
              
              .status {
                font-size: 12px;
                opacity: 0.8;
                &.returning {
                  color: #ff4d4f !important;
                }
              }
            }
            
            .task-name {
              font-size: 12px;
              margin-top: 0;
              line-height: 1.5;
            }
          }
          
          .task-continuous {
            flex: 1;
            text-align: center;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 4px;

            .zhuyi-icon {
              width: 14px;
              height: 14px;
            }
          }
          
          .task-actions {
            flex: 1;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            
            .flight-btn {
              height: 24px;
              padding: 0 12px;
              font-size: 11px;
              font-weight: 600;
              border-radius: 12px;
              display: flex;
              align-items: center;
              gap: 4px;
              
              .flight-icon {
                width: 14px;
                height: 14px;
              }

              &:hover {
                background: rgb(242, 242, 242) !important;
                border-color: rgb(242, 242, 242) !important;
                color: rgb(27, 102, 215) !important;
              }
            }

            :deep(.ant-btn-link) {
              color: white;
              &:hover {
                color: rgba(255, 255, 255, 0.8);
              }
            }
          }
        }
        
        .add-task-btn {
          width: 30px;
          height: 30px;
          border-radius: 50%;
          border: 1px dashed #d9d9d9;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s;
          
          &:hover {
            border-color: #1890ff;
            color: #1890ff;
          }
        }
      }
    }
  }
}

.custom-select {
  width: 200px;
  
  :deep(.ant-select-selector) {
    border-radius: 20px !important;
    height: 32px !important;
    line-height: 32px !important;
    padding: 0 11px !important;
  }
  
  :deep(.ant-select-selection-item) {
    line-height: 30px !important;
  }

  :deep(.ant-select-selection-search-input) {
    height: 30px !important;
  }
}

.month-calendar {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  margin-bottom: 60px;
  height: calc(100vh - 200px);
  overflow-y: auto;
  
  .calendar-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
    margin-bottom: 16px;
    position: sticky;
    top: 0;
    background: #fff;
    z-index: 1;
    background-color: #f5f5f5;
    padding: 8px;
    border-radius: 4px;
    
    .week-day {
      text-align: center;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
    }
  }
  
  .calendar-body {
    .calendar-week {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      gap: 8px;
      margin-bottom: 8px;
      
      .calendar-day {
        min-height: 100px;
        padding: 8px;
        border: 1px solid #f0f0f0;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;
        
        &:hover {
          background-color: #f5f5f5;
          border-color: #1890ff;
        }
        
        &.today {
          border: 2px solid #1890ff;
        }
          // transition: all 0.3s ease;
        
        // &:hover {
        //   border-color: #1890ff;
        //   background-color: #f0f8ff;
        //   transform: translateY(-2px);
        //   box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
        // }
        
        &.other-month {
          background-color: #fafafa;
          // cursor: default;
          
          // &:hover {
          //   border-color: #f0f0f0;
          //   background-color: #fafafa;
          //   transform: none;
          //   box-shadow: none;
          // }
          
          .day-number {
            color: rgba(0, 0, 0, 0.25);
          }
        }
        
        .day-number {
          font-size: 18px;
          font-weight: 600;
          margin-bottom: 8px;
        }
        
        .task-counts {
          .total-count {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.65);
            text-align: center;
            margin-top: 4px;
            background-color: #f5f5f5;
            border-radius: 12px;
            padding: 4px 8px;
            display: inline-block;
          }
        }
      }
    }
  }
}

// 新增外部图例样式
.task-types-legend {
  position: fixed;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 16px;
  background: rgba(255, 255, 255, 0.9);
  padding: 12px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  
  .legend-item {
    display: flex;
    align-items: center;
    
    .type-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 4px;
    }
    
    .type-name {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.65);
    }
  }
}

/* 注释掉飞行任务失败样式 */
/* .task-failed {
  color: #ffad20;
  margin-left: 4px;
} */

.task-returning {
  color: #ff4d4f;
  margin-left: 4px;
}
</style>
