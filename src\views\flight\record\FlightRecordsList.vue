<template>
  <div class="flight-records">
    <div class="p-4">

      
      <!-- 搜索框 -->
      <div class="search-filter">
        <div class="search-wrapper">
          <a-auto-complete
            v-model:value="searchValue"
            :options="searchOptions"
            placeholder="请输入记录名称进行搜索"
            style="width: 220px"
            allowClear
            class="custom-search-input"
            @search="handleSearchInput"
            @select="handleSelect"
            @pressEnter="handleSearch"
          >
            <template #prefix>
              <SearchOutlined style="color: rgba(0, 0, 0, 0.45);" />
            </template>
            <template #suffix>
              <a-button 
                type="link" 
                class="search-btn" 
                @click="handleSearch"
                :disabled="!searchValue.trim()"
              >
                搜索
              </a-button>
            </template>
          </a-auto-complete>
        </div>
        <div class="button-group">
          <a-button v-if="hasPermission('flight:flight_records:add')" type="primary" @click="handleAdd" class="action-btn">
            <PlusOutlined class="btn-icon" />
            <span>新增</span>
          </a-button>
          <a-button v-if="hasPermission('flight:flight_records:exportXls')" type="primary" @click="onExportXls" class="action-btn">
            <ExportOutlined class="btn-icon" />
            <span>导出</span>
          </a-button>
          <j-upload-button v-if="hasPermission('flight:flight_records:importExcel')" type="primary" @click="onImportXls" class="action-btn">
            <ImportOutlined class="btn-icon" />
            <span>导入</span>
          </j-upload-button>
          <super-query :config="superQueryConfig" @search="handleSuperQuery" />
          <a-button
            v-if="hasPermission('flight:flight_records:delete')"
            type="primary"
            @click="batchHandleDelete"
            class="action-btn"
            :disabled="!selectedRowKeys.length"
          >
            <DeleteOutlined class="btn-icon" />
            <span>批量处理</span>
          </a-button>
        </div>
      </div>

      <!-- 表格标题栏 -->
      <div class="task-header">
        <div class="task-checkbox">
          <a-checkbox 
            :checked="isAllSelected" 
            :indeterminate="isIndeterminate"
            @change="onCheckAllChange"
          />
        </div>
        <div class="task-id">记录ID</div>
        <div class="task-name">记录名称</div>
        <div class="task-photo-nums">照片数量</div>
        <div class="task-issue-nums">问题照片数量</div>
        <div class="task-distance">飞行距离(Km)</div>
        <div class="task-status">照片状态</div>
        <div class="task-task-id">任务ID</div>
        <div class="task-create-time">
          创建时间
          <span class="sort-icon" @click="handleSort">
            <SortAscendingOutlined v-if="sortOrder === 'asc'" class="active" />
            <SortDescendingOutlined v-else-if="sortOrder === 'desc'" class="active" />
            <SortAscendingOutlined v-else class="inactive" />
          </span>
        </div>
        <div class="task-action">操作</div>
      </div>

      <!-- 列表容器 -->
      <div class="list-container">
        <div class="task-list" v-loading="loading">
          <div v-if="!tableData.length" class="empty-list">
            <a-empty description="暂无数据" />
          </div>
          <div v-else>
            <div class="task-item" v-for="item in tableData" :key="item.id">
              <div class="task-content">
                <div class="task-info">
                  <div class="task-checkbox">
                    <a-checkbox 
                      :checked="selectedRowKeys.includes(item.id)"
                      @change="(e) => onCheckChange(e, item)"
                    />
                  </div>
                  <div class="task-id">{{ item.id || '-' }}</div>
                  <div class="task-name">{{ item.recordName || '-' }}</div>
                  <div class="task-photo-nums">{{ item.photoNums || '-' }}</div>
                  <div class="task-issue-nums">{{ item.issuePhotoNums || '-' }}</div>
                  <div class="task-distance">{{ item.flightDistance || '-' }}</div>
                  <div class="task-status">
                    <a-tag :color="getStatusColor(item.photoStatus)">
                      {{ getStatusText(item.photoStatus) }}
                    </a-tag>
                  </div>
                  <div class="task-task-id">{{ item.taskId || '-' }}</div>
                  <div class="task-create-time">{{ item.createTime || '-' }}</div>
                  <div class="task-action">
                    <a-dropdown>
                      <a-button type="text" class="setting-btn">
                        <SettingOutlined style="font-size: 16px; color: rgba(0, 0, 0, 0.65);" />
                      </a-button>
                      <template #overlay>
                        <a-menu>
                          <template v-if="hasPermission('flight:flight_records:edit')">
                            <a-menu-item key="edit" @click="handleEdit(item)">
                              <EditOutlined />
                              <span>编辑</span>
                            </a-menu-item>
                          </template>
                          <a-menu-item key="detail" @click="handleDetail(item)">
                            <EyeOutlined />
                            <span>详情</span>
                          </a-menu-item>
                          <a-menu-item key="sync" @click="handleImport(item)" class="sync-item">
                            <SvgIcon name="Sync" :size="14" />
                            <span>同步</span>
                          </a-menu-item>
                          <template v-if="hasPermission('flight:flight_records:delete')">
                            <a-menu-item key="delete" @click="handleDelete(item)" class="danger-item">
                              <DeleteOutlined />
                              <span>删除</span>
                            </a-menu-item>
                          </template>
                        </a-menu>
                      </template>
                    </a-dropdown>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页组件 -->
        <div class="pagination-wrapper" v-if="total > 0">
          <a-pagination
            v-model:current="currentPage"
            :total="total"
            :pageSize="pageSize"
            :showSizeChanger="false"
            :showQuickJumper="false"
            :showTotal="(total) => `共有 ${total} 条数据`"
            @change="handlePageChange"
            class="custom-pagination"
          />
        </div>
      </div>
    </div>
    <!-- 表单区域 -->
    <component :is="FlightRecordsModal.default" @register="registerModal" @success="handleSuccess" />
    
    <!-- 添加同步结果对话框 -->
    <a-modal
      v-model:visible="syncResultVisible"
      :closable="true"
      :maskClosable="true"
      :footer="null"
      title="同步结果"
      :width="500"
    >
      <div class="sync-result-container">
        <div class="sync-status">
          <a-result
            :status="syncStatus"
            :title="getSyncStatusTitle()"
            :sub-title="getSyncStatusSubtitle()"
          />
        </div>
        <div class="sync-stats">
          <a-descriptions bordered :column="2">
            <a-descriptions-item label="新增图片">{{ syncResults.inserted || 0 }}</a-descriptions-item>
            <a-descriptions-item label="更新图片">{{ syncResults.updated || 0 }}</a-descriptions-item>
            <a-descriptions-item label="跳过图片">{{ syncResults.skipped || 0 }}</a-descriptions-item>
            <a-descriptions-item label="错误数量">{{ syncResults.errors || 0 }}</a-descriptions-item>
            <a-descriptions-item label="MinIO图片总数">{{ syncResults.total || 0 }}</a-descriptions-item>
            <a-descriptions-item label="实际处理数">{{ (syncResults.inserted || 0) + (syncResults.updated || 0) + (syncResults.skipped || 0) }}</a-descriptions-item>
            <a-descriptions-item label="耗时(秒)">{{ syncResults.elapsedSeconds || 0 }}</a-descriptions-item>
            <a-descriptions-item label="成功率" v-if="syncResults.total > 0">
              {{ Math.round(((syncResults.inserted + syncResults.updated) / syncResults.total) * 100) }}%
            </a-descriptions-item>
          </a-descriptions>
          
          <!-- 同步前后对比信息 -->
          <div v-if="syncResults.beforeStatus" class="sync-comparison" style="margin-top: 16px;">
            <a-divider orientation="left">同步前后对比</a-divider>
            <a-descriptions bordered :column="3" size="small">
              <a-descriptions-item label="状态变化">
                <a-tag :color="getStatusColor(syncResults.beforeStatus)">{{ syncResults.beforeStatus }}</a-tag>
                <ArrowRightOutlined style="margin: 0 8px; color: #999;" />
                <a-tag :color="getStatusColor(getNewStatus())">{{ getNewStatus() }}</a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="照片数量说明" span="2">
                <div style="font-size: 12px; color: #666; line-height: 1.4;">
                  <div>• MinIO中发现 {{ syncResults.total || 0 }} 张图片</div>
                  <div>• 新增到数据库 {{ syncResults.inserted || 0 }} 张</div>
                  <div>• 更新已有图片 {{ syncResults.updated || 0 }} 张</div>
                  <div>• 跳过重复图片 {{ syncResults.skipped || 0 }} 张</div>
                  <div style="color: #1890ff; font-weight: 500;">
                    • 数据库中该任务图片总数需要后端重新统计
                  </div>
                </div>
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </div>
        <div class="sync-actions">
          <a-button type="primary" @click="closeSyncResultModal">
            确定
          </a-button>
        </div>
      </div>
    </a-modal>
    
    <!-- 添加进度条对话框 -->
    <a-modal
      v-model:visible="syncProgressVisible"
      :closable="true"
      :maskClosable="false"
      :footer="null"
      title="正在同步图片"
      :width="500"
      @cancel="handleCancelSync"
    >
      <div class="sync-progress-container">
        <a-progress :percent="syncProgress" status="active" />
        <p class="sync-progress-text">正在从MinIO同步图片，请耐心等待...</p>
        <p class="sync-progress-hint">预计总时间: {{expectedTime}}秒</p>
      </div>
    </a-modal>

  </div>

</template>

<script lang="ts" name="flight-flightRecords" setup>
  import {ref, reactive, computed, watch, onMounted} from 'vue';
  import {useModal} from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage'
  import * as FlightRecordsModal from './components/FlightRecordsModal.vue'
  import {columns, searchFormSchema, superQuerySchema} from './FlightRecords.data';
  import {deleteOne, batchDelete} from './FlightRecords.api';
  import { defHttp } from '/@/utils/http/axios';
  import { message, Modal } from 'ant-design-vue';
  import { getMinioImageCount, smartSyncMinioImages} from '/@/utils/minio-image/syncMinioImages';
  import type { CheckboxProps } from 'ant-design-vue';
  import { usePermission } from '/@/hooks/web/usePermission';
  // 导入Ant Design Vue图标组件 - 这些图标在模板中被使用
  import {
    SearchOutlined,
    PlusOutlined,
    ExportOutlined,
    ImportOutlined,
    DeleteOutlined,
    EditOutlined,
    EyeOutlined,
    SettingOutlined,
    SortAscendingOutlined,
    SortDescendingOutlined,
    ArrowRightOutlined
  } from '@ant-design/icons-vue';
  import { SvgIcon } from '/@/components/Icon';

  // 确保图标组件在模板中可用
  const iconComponents = {
    SearchOutlined,
    PlusOutlined,
    ExportOutlined,
    ImportOutlined,
    DeleteOutlined,
    EditOutlined,
    EyeOutlined,
    SettingOutlined,
    SortAscendingOutlined,
    SortDescendingOutlined,
    ArrowRightOutlined
  };

  const API = {
    // 飞行记录相关接口
    LIST: '/flight/flightRecords/list',  // 获取飞行记录列表
    ADD: '/flight/flightRecords/add',    // 添加飞行记录
    EDIT: '/flight/flightRecords/edit',  // 编辑飞行记录
    DELETE: '/flight/flightRecords/delete', // 删除单条记录
    BATCH_DELETE: '/flight/flightRecords/deleteBatch', // 批量删除
    QUERY_BY_ID: '/flight/flightRecords/queryById', // 根据ID查询
    EXPORT: '/flight/flightRecords/exportXls', // 导出Excel
    IMPORT: '/flight/flightRecords/importExcel', // 导入Excel
    
    // 照片同步相关接口
    SYNC_MINIO: '/data/inspection/syncMinioImages', // 同步MinIO图片
    UPDATE_PHOTO_STATUS: '/flight/flightRecords/updatePhotoStatus', // 更新照片同步状态
    GET_IMAGE_COUNT: '/data/inspection/getMinioImageCount', // 获取MinIO图片数量
  };

  const queryParam = reactive<any>({});
  const searchValue = ref('');
  const searchOptions = ref<{ value: string; label: string }[]>([]);
  const tableData = ref<any[]>([]);
  const loading = ref(false);
  const originalList = ref<any[]>([]);
  const currentPage = ref(1);
  const pageSize = ref(10);
  const total = ref(0);
  const sortOrder = ref<'desc' | 'asc' | null>('desc');
  const [registerModal, {openModal}] = useModal();
  const { hasPermission } = usePermission();

  //注册table数据
  const {tableContext, onExportXls, onImportXls } = useListPage({
    tableProps:{
      title: '飞行记录',
      columns,
      canResize:false,
      formConfig: {
        schemas: searchFormSchema,
        autoSubmitOnEnter:true,
        showAdvancedButton:true,
        fieldMapToNumber: [],
        fieldMapToTime: [],
      },
      actionColumn: {
        width: 120,
        fixed:'right'
      },
      beforeFetch: (params) => {
        console.log('beforeFetch 参数:', params);
        return {
          ...params,
          ...queryParam,
          pageNo: currentPage.value,
          pageSize: pageSize.value,
          recordName: searchValue.value.trim() || undefined,
          column: sortOrder.value ? 'createTime' : undefined,
          order: sortOrder.value || undefined
        };
      },
    },
    exportConfig: {
      name:"飞行记录",
      url: API.EXPORT,
      params: queryParam,
    },
    importConfig: {
      url: API.IMPORT,
      success: handleSuccess
    },
  });

  const [registerTable, {reload}, { rowSelection, selectedRowKeys }] = tableContext;

  // 加载数据
  const loadData = async () => {
    try {
      loading.value = true;
      const res = await defHttp.get({
        url: API.LIST,
        params: {
          pageNo: currentPage.value,
          pageSize: pageSize.value,
          recordName: searchValue.value.trim() || undefined,
          column: sortOrder.value ? 'createTime' : undefined,
          order: sortOrder.value || undefined,
          ...queryParam
        }
      });

      if (res?.records) {
        tableData.value = res.records.map(item => ({
          ...item,
          id: item.id || '-',
          recordName: item.recordName || '-',
          photoNums: item.photoNums ?? '-',
          issuePhotoNums: item.issuePhotoNums ?? '-',
          flightDistance: item.flightDistance || '-',
          photoStatus: item.photoStatus ?? '未同步',
          taskId: item.taskId || '-',
          createTime: item.createTime || item.takeoffTime || '-'
        }));

        total.value = res.total || 0;
        originalList.value = [...tableData.value];
      } else {
        tableData.value = [];
        total.value = 0;
        originalList.value = [];
      }
    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败');
    } finally {
      loading.value = false;
    }
  };

  // 分页变化处理
  const handlePageChange = async (page: number) => {
    currentPage.value = page;
    await loadData();
  };

  // 搜索处理
  const handleSearch = async () => {
    currentPage.value = 1;
    await loadData();
  };

  // 搜索输入处理
  const handleSearchInput = (value: string) => {
    if (!value) {
      searchOptions.value = [];
      return;
    }
    const searchText = value.toLowerCase();
    const filteredOptions = originalList.value
      .filter(item => item.recordName?.toLowerCase().includes(searchText))
      .map(item => ({
        value: item.recordName,
        label: item.recordName
      }))
      .filter((item, index, self) => 
        index === self.findIndex(t => t.value === item.value)
      )
      .slice(0, 10);
    searchOptions.value = filteredOptions;
  };

  // 选择处理
  const handleSelect = (value: string) => {
    searchValue.value = value;
    handleSearch();
  };

  // 组件挂载时加载数据
  onMounted(() => {
    loadData();
  });

  // 监听分页和排序变化
  watch([currentPage, pageSize, sortOrder], loadData);

  /**
   * 新增事件
   */
  function handleAdd() {
    openModal(true, {
      isUpdate: false,
      showFooter: true,
    });
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: true,
    });
  }

  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    try {
      await new Promise((resolve, reject) => {
        Modal.confirm({
          title: '确认删除',
          content: '确定要删除这条记录吗？',
          okText: '确认',
          cancelText: '取消',
          onOk: resolve,
          onCancel: reject,
        });
      });

      await deleteOne({id: record.id}, () => {
        message.success('删除成功');
        loadData();
      });
    } catch (error) {
      if (error) {
        message.error('删除失败');
      }
    }
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    if (selectedRowKeys.value.length === 0) {
      message.warning('请选择要删除的记录');
      return;
    }

    try {
      await new Promise((resolve, reject) => {
        Modal.confirm({
          title: '确认删除',
          content: `确定要删除选中的 ${selectedRowKeys.value.length} 条记录吗？`,
          okText: '确认',
          cancelText: '取消',
          onOk: resolve,
          onCancel: reject,
        });
      });

      await batchDelete({ids: selectedRowKeys.value}, () => {
        message.success('批量删除成功');
        selectedRowKeys.value = [];
        loadData();
      });
    } catch (error) {
      if (error) {
        message.error('批量删除失败');
      }
    }
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    selectedRowKeys.value = [];
    loadData();
  }

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    currentPage.value = 1;
    loadData();
  }

  // 添加状态相关的计算属性
  const isAllSelected = computed(() => {
    return tableData.value.length > 0 && selectedRowKeys.value.length === tableData.value.length;
  });

  const isIndeterminate = computed(() => {
    return selectedRowKeys.value.length > 0 && selectedRowKeys.value.length < tableData.value.length;
  });

  // 全选/取消全选
  const onCheckAllChange = (e: { target: CheckboxProps }) => {
    selectedRowKeys.value = e.target.checked ? tableData.value.map(item => item.id) : [];
  };

  // 单个选择
  const onCheckChange = (e: { target: CheckboxProps }, record: any) => {
    if (e.target.checked) {
      selectedRowKeys.value = [...selectedRowKeys.value, record.id];
    } else {
      selectedRowKeys.value = selectedRowKeys.value.filter(id => id !== record.id);
    }
  };

  // -----同步结果相关状态
  const syncResultVisible = ref(false);
  const syncStatus = ref<'success' | 'error' | 'info'>('info');
  const syncResults = reactive({
    inserted: 0,
    updated: 0, 
    skipped: 0,
    errors: 0,
    total: 0,
    elapsedSeconds: 0,
    objectCount: 0,
    beforePhotoNums: 0,
    beforeIssuePhotoNums: 0,
    beforeStatus: '未同步'
  });

  // 进度条相关状态
  const syncProgressVisible = ref(false);
  const syncProgress = ref(0);
  const expectedTime = ref(0);
  let progressInterval: any = null;

  // 获取同步状态标题
  function getSyncStatusTitle() {
    if (syncStatus.value === 'success') {
      return '同步成功';
    } else if (syncStatus.value === 'error') {
      return '同步完成但有错误';
    } else {
      return '同步信息';
    }
  }

  // 排序处理
  const handleSort = () => {
    if (sortOrder.value === 'desc') {
      sortOrder.value = null;
    } else if (sortOrder.value === null) {
      sortOrder.value = 'asc';
    } else {
      sortOrder.value = 'desc';
    }
    currentPage.value = 1;
    loadData();
  };

  // -----获取同步状态副标题
  function getSyncStatusSubtitle() {
    const inserted = syncResults.inserted || 0;
    const updated = syncResults.updated || 0;
    const skipped = syncResults.skipped || 0;
    const errors = syncResults.errors || 0;
    const total = syncResults.total || 0;
    const elapsedSeconds = syncResults.elapsedSeconds || 0;
    
    const successCount = inserted + updated;
    
    if (successCount > 0 && errors === 0) {
      // 完全成功
      if (inserted > 0 && updated > 0) {
        return `从MinIO发现${total}张图片，新增${inserted}张，更新${updated}张，跳过${skipped}张，耗时${elapsedSeconds}秒`;
      } else if (inserted > 0) {
        return `从MinIO发现${total}张图片，新增${inserted}张到数据库，跳过${skipped}张已存在的，耗时${elapsedSeconds}秒`;
      } else if (updated > 0) {
        return `从MinIO发现${total}张图片，更新${updated}张已有图片，跳过${skipped}张无变化的，耗时${elapsedSeconds}秒`;
      } else {
        return `从MinIO发现${total}张图片，全部${skipped}张均已是最新状态，无需处理，耗时${elapsedSeconds}秒`;
      }
    } else if (successCount > 0 && errors > 0) {
      // 部分成功
      return `从MinIO发现${total}张图片，成功处理${successCount}张，但有${errors}个错误，耗时${elapsedSeconds}秒`;
    } else if (errors > 0) {
      // 只有错误
      return `处理过程中发生${errors}个错误，共发现${total}张图片，耗时${elapsedSeconds}秒`;
    } else if (total === 0) {
      // 没有找到图片
      return '未在MinIO中找到该任务对应的图片文件，请检查任务ID是否正确';
    } else {
      // 其他情况
      return `发现${total}张图片，但未发现需要处理的内容，耗时${elapsedSeconds}秒`;
    }
  }

  // 状态映射配置
  const statusConfig = {
    '未同步': { color: 'blue', text: '未同步' },
    '同步完成': { color: 'green', text: '同步完成' },
    '同步失败': { color: 'red', text: '同步失败' },
    null: { color: 'default', text: '-' },
    undefined: { color: 'default', text: '-' },
    '': { color: 'default', text: '-' }
  };

  const getStatusColor = (status) => statusConfig[status]?.color || 'default';
  const getStatusText = (status) => statusConfig[status]?.text || '-';

  // -----关闭结果对话框
  function closeSyncResultModal() {
    syncResultVisible.value = false;
  }

  /**
   * 模拟进度更新
   */
  function startProgressSimulation(estimatedCount: number) {
    const totalTime = Math.max(10, Math.ceil(estimatedCount / 30));
    expectedTime.value = totalTime;

    if (progressInterval) clearInterval(progressInterval);

    syncProgress.value = 0;
    syncProgressVisible.value = true;

    const updateInterval = 200;
    const increment = 90 / ((totalTime * 1000 * 0.9) / updateInterval);

    progressInterval = setInterval(() => {
      if (syncProgress.value < 90) {
        syncProgress.value += increment;
      } else if (!syncProgressVisible.value) {
        clearInterval(progressInterval);
      }
    }, updateInterval);
  }

  /**
   * 完成进度并显示结果
   */
  function completeProgress(success: boolean = true) {
    // 清除进度模拟定时器
    if (progressInterval) {
      clearInterval(progressInterval);
      progressInterval = null;
    }

    // 设置为100%完成并显示结果
    syncProgress.value = 100;
    setTimeout(() => {
      syncProgressVisible.value = false;
      syncResultVisible.value = true;
    }, 500);
  }

  /**
   * 处理取消同步操作
   */
  function handleCancelSync() {
    Modal.confirm({
      title: '确认取消同步',
      content: '同步操作可能仍在后台进行，确定要关闭进度窗口吗？您可以稍后刷新页面查看同步结果。',
      okText: '确认关闭',
      cancelText: '继续等待',
      onOk: () => {
        // 清除进度模拟定时器
        if (progressInterval) {
          clearInterval(progressInterval);
          progressInterval = null;
        }

        // 关闭进度窗口
        syncProgressVisible.value = false;
        syncProgress.value = 0;

        // 提示用户
        message.info('已关闭同步进度窗口，同步操作可能仍在后台进行，请稍后刷新页面查看结果');

        // 3秒后自动刷新数据
        setTimeout(() => {
          loadData();
        }, 3000);
      }
    });
  }

  /**
   * 处理同步错误
   */
  async function handleSyncError(error: any, taskId?: any) {
    console.error('同步失败:', error);
    const errorMsg = error?.message || '未知错误';

    if (progressInterval) {
      clearInterval(progressInterval);
      progressInterval = null;
    }
    syncProgressVisible.value = false;

    syncStatus.value = 'error';
    syncResults.errors = 1;
    syncResults.inserted = 0;
    syncResults.updated = 0;
    syncResults.skipped = 0;
    syncResults.total = 0;

    if (taskId) {
      try {
        const { updatePhotoStatus } = await import('./FlightRecords.api');
        await updatePhotoStatus({
          taskId: taskId,
          status: 2,
          insertCount: 0,
          updateCount: 0,
          errorCount: 1
        });
      } catch (statusError) {
        console.error('更新照片状态失败:', statusError);
      }
    }

    if (errorMsg.includes('timeout')) {
      message.error(`请求超时，但同步操作可能仍在后台进行。请稍后刷新页面查看结果。`);
    } else {
      message.error(`同步失败: ${errorMsg}`);
    }

    syncResultVisible.value = true;
    setTimeout(() => loadData(), 3000);
  }

  /**
   * 合并 updateTaskPhotoStatus 和 部分 handleSyncResult 逻辑
   */
  async function updateSyncStatus(taskId: any, result: any) {
    if (!result) return null;

    try {
      Object.keys(result).forEach(key => {
        if (key in syncResults) {
          syncResults[key] = result[key];
        }
      });

      const insertCount = result.inserted || 0;
      const updateCount = result.updated || 0;
      const skipCount = result.skipped || 0;
      const errorCount = result.errors || 0;

      const actualPhotoCount = result.total - skipCount - errorCount;

      const hasErrors = errorCount > 0;
      const hasChanges = actualPhotoCount > 0;
      const status = hasErrors ? 2 : (hasChanges ? 1 : 0);

      syncStatus.value = hasErrors ? 'error' : (hasChanges ? 'success' : 'info');

      await defHttp.post({
        url: API.UPDATE_PHOTO_STATUS,
        data: {
          taskId: taskId,
          status: status,
          insertCount: insertCount,
          updateCount: updateCount,
          errorCount: errorCount,
          photoNums: actualPhotoCount
        }
      });

      return status;
    } catch (error) {
      console.error('更新照片状态失败:', error);
      return null;
    }
  }

  /**
   * 处理同步结果
   */
  async function handleSyncResult(result: any, taskId: any) {
    if (!result) return;

    await updateSyncStatus(taskId, result);
    message.loading('正在获取最新照片统计数据...', 2);

    await refreshDataWithRetry(taskId, 3);

    completeProgress(!(result.errors > 0));

    showSyncResultMessage(result);
  }

  // 封装重试刷新数据逻辑
  async function refreshDataWithRetry(taskId: any, maxRetries = 3) {
    for (let i = 0; i < maxRetries; i++) {
      await new Promise(resolve => setTimeout(resolve, 1500));
      await loadData();

      const updatedRecord = tableData.value.find(item => item.taskId === taskId);
      if (updatedRecord && updatedRecord.photoNums !== '-') {
        break;
      }
    }
  }

  // 封装结果消息显示
  function showSyncResultMessage(result: any) {
    const insertCount = result.inserted || 0;
    const updateCount = result.updated || 0;
    const skipCount = result.skipped || 0;
    const errorCount = result.errors || 0;
    const total = insertCount + updateCount;

    if (total > 0) {
      const changeMsg = `新增${insertCount}张，更新${updateCount}张`;
      if (errorCount > 0) {
        message.warning(`同步部分完成：${changeMsg}，但有${errorCount}个错误`);
      } else {
        message.success(`同步成功：${changeMsg}，总耗时${result.elapsedSeconds || 0}秒`);
      }
    } else if (skipCount > 0) {
      message.info(`所有图片均已是最新状态，跳过${skipCount}张`);
    } else {
      message.info('同步完成，未发现需要处理的图片');
    }
  }

  /**
   * 执行同步操作
   */
  async function performSync(taskId: any) {
    if (!taskId) {
      message.error('未找到有效的任务ID，无法同步');
      return null;
    }

    const taskIdStr = taskId.toString().trim();

    // 记录同步前的状态
    const beforeSyncRecord = tableData.value.find(item => item.taskId === taskIdStr);
    const beforePhotoNums = beforeSyncRecord?.photoNums || 0;
    const beforeIssuePhotoNums = beforeSyncRecord?.issuePhotoNums || 0;
    const beforeStatus = beforeSyncRecord?.photoStatus || '未同步';

    // 获取当前任务的图片数量
    let estimatedCount = 0;
    try {
      estimatedCount = await getMinioImageCount(undefined, taskIdStr) || 30;
    } catch (error) {
      estimatedCount = 30;
    }

    startProgressSimulation(estimatedCount);

    try {
      const result = await smartSyncMinioImages(taskIdStr, false);

      if (result) {
        syncResults.beforePhotoNums = beforePhotoNums;
        syncResults.beforeIssuePhotoNums = beforeIssuePhotoNums;
        syncResults.beforeStatus = beforeStatus;
      }

      await handleSyncResult(result, taskId);
      return result;
    } catch (error) {
      completeProgress(false);
      handleSyncError(error, taskId);
      return null;
    }
  }

  /**
   * 同步任务图片并显示结果 - 供其他组件调用
   */
  async function syncTaskImagesAndShowResult(taskId: any) {
    if (!taskId) {
      message.error('未找到有效的任务ID，无法同步');
      return null;
    }

    return await performSync(taskId);
  }

  /**
   * 导入MinIO图片事件 - 主函数，用户点击同步按钮调用
   */
  async function handleImport(record: any) {
    const currentStatus = record?.photoStatus;
    const isAlreadySynced = currentStatus === '同步完成' || currentStatus === '同步失败';

    let confirmContent = `确定要从MinIO同步【任务ID: ${record?.taskId || '未知'}】的图片到数据库吗？该操作可能需要1-2分钟，请耐心等待。`;

    if (isAlreadySynced) {
      confirmContent = `该任务的图片状态为"${currentStatus}"，重新同步将会：
      1. 检查MinIO中是否有新增的图片并添加到数据库
      2. 更新已存在图片的信息（如有变化）
      3. 重新计算照片数量统计

      确定要重新同步【任务ID: ${record?.taskId || '未知'}】的图片吗？`;
    }

    Modal.confirm({
      title: isAlreadySynced ? '重新同步MinIO图片' : '同步MinIO图片',
      content: confirmContent,
      okText: '确认',
      cancelText: '取消',
      width: 500,
      onOk: async () => {
        setTimeout(async () => {
          try {
            const taskId = record?.taskId;
            if (!taskId) {
              message.error('未找到有效的任务ID，无法同步');
              return;
            }

            await performSync(taskId);
          } catch (error) {
            handleSyncError(error, record?.taskId);
          }
        }, 0);
      }
    });
  }

  // 暴露组件方法
  defineExpose({
    syncTaskImagesAndShowResult,
    handleImport
  });

  // 获取新的同步状态
  const getNewStatus = () => {
    const hasErrors = syncResults.errors > 0;
    const hasChanges = (syncResults.inserted || 0) + (syncResults.updated || 0) > 0;
    
    if (hasErrors && hasChanges) {
      return '同步完成但有错误';
    } else if (hasErrors) {
      return '同步失败';
    } else if (hasChanges) {
      return '同步完成';
    } else {
      return '同步完成';
    }
  };


</script>

<style lang="less" scoped>
.flight-records {
  background: #fff;
  height: 100vh;
  display: flex;
  flex-direction: column;

  .p-4 {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    height: 100%;
    padding-bottom: 0;
  }

  .search-filter {
    margin: 14px 0 16px;
    padding-left: 24px;
    position: relative;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .search-wrapper {
      position: relative;
      width: 220px;
      
      :deep(.custom-search-input) {
        .ant-select-selector {
          border-radius: 50px !important;
          border: 1px solid #9c9b9b !important;
          transition: all 0.3s;
          overflow: hidden;
          background: #fff;
          height: 32px !important;
          padding: 4px 11px !important;

          .ant-select-selection-search-input {
            height: 24px !important;
            line-height: 24px !important;
            margin-top: 2px !important;
          }

          .ant-select-selection-placeholder {
            line-height: 24px !important;
            margin-top: 0px !important;
          }

          .ant-select-selection-item {
            line-height: 24px !important;
            margin-top: 1px !important;
          }
        }

        .ant-select-dropdown {
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

          .ant-select-item {
            padding: 8px 12px;
            font-size: 14px;
            transition: all 0.3s;

            &:hover {
              background-color: #f5f5f5;
            }

            &-option-selected {
              background-color: #e6f7ff;
              color: #1890ff;
            }
          }
        }

        .ant-input-suffix {
          margin-left: 0;
          padding-left: 4px;
          border-left: 1px solid #d9d9d9;

          .search-btn {
            height: 22px;
            padding: 0 4px;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
            border: none;
            background: transparent;
            transition: all 0.3s;

            &:hover {
              color: #1890ff;
            }

            &:disabled {
              color: rgba(0, 0, 0, 0.25);
              cursor: not-allowed;
            }
          }
        }
      }
    }

    .button-group {
      display: flex;
      align-items: center;
      gap: 12px;

      :deep(.ant-btn-primary),
      :deep(.j-upload-button) {
        height: 31px;
        padding: 0 13px;
        border-radius: 20px;
        font-size: 14px;
        display: inline-flex;
        align-items: center;
        gap: 3px;
        transition: all 0.3s;

        .btn-icon {
          font-size: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 2px;
        }

        span {
          line-height: 1;
        }

        &:hover {
          opacity: 0.85;
          .btn-icon {
            transform: scale(1.1);
          }
        }

        &:active {
          opacity: 0.7;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }

      /* 高级查询按钮样式 */
      :deep(.j-super-query-button) {
        .ant-btn-primary {
          height: 31px;
          padding: 0 13px;
          border-radius: 20px;
          font-size: 14px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s;

          &:hover {
            opacity: 0.85;
          }

          &:active {
            opacity: 0.7;
          }
        }
      }

      .action-btn {
        height: 31px;
        padding: 0 13px;
        display: inline-flex;
        align-items: center;
        gap: 3px;
        border-radius: 20px;
        font-size: 14px;
        transition: all 0.3s;

        .btn-icon {
          font-size: 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 2px;
        }

        span {
          line-height: 1;
        }

        &:hover {
          opacity: 0.85;
          .btn-icon {
            transform: scale(1.1);
          }
        }

        &:active {
          opacity: 0.7;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }
  }

  .task-header {
    display: flex;
    align-items: center;
    padding: 8px 0;
    margin-bottom: 12px;
    flex-shrink: 0;
    background: #fafafa;
    border-radius: 4px;
    padding: 12px;

    > div {
      color: rgba(0, 0, 0, 0.45);
      font-size: 14px;
      font-weight: 500;
      padding: 0 8px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .task-checkbox { flex: 0.2; }
    .task-id { flex: 0.6; }
    .task-name { flex: 0.5; }
    .task-photo-nums { flex: 0.4; }
    .task-issue-nums { flex: 0.5; }
    .task-distance { flex: 0.6; }
    .task-status { flex: 0.5; }
    .task-task-id { flex: 0.5; }
    .task-create-time { 
      flex: 0.9;
      display: flex;
      align-items: center;
      gap: 4px;
      cursor: pointer;

      .sort-icon {
        display: inline-flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.45);
        transition: all 0.3s;
        margin-left: 2px;

        .anticon {
          font-size: 14px;
          transition: all 0.3s;
          transform: scale(0.9);

          &.active {
            color: #1890ff;
            transform: scale(1);
          }

          &.inactive {
            opacity: 0.5;
          }
        }

        &:hover {
          .anticon.inactive {
            opacity: 1;
            color: #1890ff;
            transform: scale(1);
          }
        }
      }
    }
    .task-action { flex: 0.3; }
  }

  .list-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    margin-bottom: 52px;

    .task-list {
      flex: 1;
      overflow-y: auto;
      padding-right: 0px;
      height: calc(100vh - 260px);
      margin-bottom: 0;

      .empty-list {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
      }

      &::-webkit-scrollbar {
        width: 5px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #d9d9d9;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background-color: #f0f0f0;
        border-radius: 3px;
      }

      .task-item {
        margin-bottom: 12px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
        }

        .task-content {
          background: #fff;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
          padding: 12px;
          display: flex;
          align-items: center;
          transition: all 0.3s ease;

          &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
          }

          .task-info {
            display: flex;
            align-items: center;
            width: 100%;
            gap: 8px;

            > div {
              padding: 0 8px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              font-size: 14px;
              color: rgba(0, 0, 0, 0.88);
            }

            .task-checkbox { flex: 0.1; }
            .task-id { flex: 0.5; }
            .task-name { flex: 1; }
            .task-photo-nums { flex: 0.5; }
            .task-issue-nums { flex: 0.5; }
            .task-distance { flex: 0.5; }
            .task-status { flex: 0.5; }
            .task-task-id { flex: 0.5; }
            .task-create-time { flex: 1; }
            .task-action { 
              flex: 0.5;
              display: flex;
              justify-content: center;
              align-items: center;
              padding: 0 8px;

              .setting-btn {
                width: 32px;
                height: 32px;
                padding: 4px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                border-radius: 4px;
                transition: all 0.3s;
                background: transparent;

                &:hover {
                  background: rgba(0, 0, 0, 0.04);
                }

                &:active {
                  background: rgba(0, 0, 0, 0.08);
                }
              }

              :deep(.ant-dropdown-menu) {
                min-width: 120px;
                padding: 4px 0;

                .ant-dropdown-menu-item {
                  padding: 8px 16px;
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  font-size: 14px;
                  line-height: 22px;

                  .anticon {
                    font-size: 14px;
                    color: rgba(0, 0, 0, 0.65);
                  }

                  span {
                    color: rgba(0, 0, 0, 0.65);
                  }

                  &:hover {
                    background: #f5f5f5;

                    .anticon, span {
                      color: #1890ff;
                    }
                  }

                  &.danger-item {
                    .anticon, span {
                      color: #ff4d4f;
                    }

                    &:hover {
                      background: #fff1f0;

                      .anticon, span {
                        color: #ff4d4f;
                      }
                    }
                  }

                  &.sync-item {
                    .anticon, span {
                      color: #1890ff;
                    }

                    &:hover {
                      background: #e6f7ff;

                      .anticon, span {
                        color: #1890ff;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  .pagination-wrapper {
    flex-shrink: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 12px 0;
    background: #fff;
    border-top: 1px solid #f0f0f0;
    margin-top: 4px;

    .custom-pagination {
      :deep(.ant-pagination-item) {
        min-width: 28px;
        height: 28px;
        line-height: 26px;
        border-radius: 20px;
        transition: all 0.3s;
        font-size: 13px;

        &:hover {
          border-color: #1890ff;
        }

        &-active {
          background: #1890ff;
          border-color: #1890ff;

          a {
            color: #fff;
          }

          &:hover {
            background: #40a9ff;
            border-color: #40a9ff;
          }
        }
      }

      :deep(.ant-pagination-prev),
      :deep(.ant-pagination-next) {
        min-width: 28px;
        height: 28px;
        line-height: 26px;

        .ant-pagination-item-link {
          border-radius: 20px;
          transition: all 0.3s;
          font-size: 13px;

          &:hover {
            border-color: #1890ff;
            color: #1890ff;
          }
        }
      }

      :deep(.ant-pagination-jump-prev),
      :deep(.ant-pagination-jump-next) {
        min-width: 28px;
        height: 28px;
        line-height: 26px;

        .ant-pagination-item-container {
          .ant-pagination-item-ellipsis {
            color: rgba(0, 0, 0, 0.45);
            font-size: 13px;
          }
        }
      }

      :deep(.ant-pagination-total-text) {
        margin-right: 16px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
      }
    }
  }

   // 同步结果样式
  .sync-result-container {
    .sync-status {
      margin-bottom: 20px;
      
      :deep(.ant-result-title) {
        color: rgba(0, 0, 0, 0.85);
        font-weight: 500;
      }
      
      :deep(.ant-result-subtitle) {
        color: rgba(0, 0, 0, 0.65);
        font-size: 14px;
        line-height: 1.5;
      }
    }
    
    .sync-stats {
      margin-bottom: 20px;
      
      :deep(.ant-descriptions-item-label) {
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
      }
      
      :deep(.ant-descriptions-item-content) {
        color: rgba(0, 0, 0, 0.65);
      }
      
      // 特殊颜色标识
      :deep(.ant-descriptions-item:nth-child(1) .ant-descriptions-item-content) {
        color: #52c41a; // 新增 - 绿色
        font-weight: 500;
      }
      
      :deep(.ant-descriptions-item:nth-child(2) .ant-descriptions-item-content) {
        color: #1890ff; // 更新 - 蓝色
        font-weight: 500;
      }
      
      :deep(.ant-descriptions-item:nth-child(4) .ant-descriptions-item-content) {
        color: #ff4d4f; // 错误 - 红色
        font-weight: 500;
      }
    }
    
    .sync-comparison {
      :deep(.ant-divider-inner-text) {
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
      }
      
      :deep(.ant-descriptions-item-content) {
        display: flex;
        align-items: center;
      }
    }
    
    .sync-actions {
      text-align: center;
      margin-top: 20px;
      
      .ant-btn {
        min-width: 80px;
      }
    }
  }

    // 同步进度样式
  .sync-progress-container {
    padding: 20px 0;

    .sync-progress-text {
      margin-top: 15px;
      text-align: center;
      color: rgba(0, 0, 0, 0.65);
    }

    .sync-progress-hint {
      text-align: center;
      color: rgba(0, 0, 0, 0.45);
      font-size: 12px;
    }

    .sync-progress-actions {
      margin-top: 20px;
      text-align: center;

      .ant-btn {
        min-width: 80px;
        border-radius: 4px;
        font-size: 12px;
        height: 28px;
        color: rgba(0, 0, 0, 0.65);
        border-color: #d9d9d9;

        &:hover {
          color: #ff4d4f;
          border-color: #ff4d4f;
        }
      }
    }
  }


}
</style>